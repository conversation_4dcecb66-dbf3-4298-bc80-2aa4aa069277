#!/usr/bin/env python3
"""
测试Bark推送功能
"""

import requests
import urllib.parse

# 使用脚本中的Bark Key
BARK_KEY = "AhMyXTSspmTD3xeu2SpMqX"

def test_bark_notification():
    """测试Bark通知"""
    title = "测试通知 🧪"
    content = "这是一条测试消息，用于验证Bark推送功能"
    
    print(f"测试Bark推送...")
    print(f"Bark Key: {BARK_KEY[:10]}...{BARK_KEY[-10:]}")
    print(f"标题: {title}")
    print(f"内容: {content}")
    
    try:
        # URL编码
        encoded_title = urllib.parse.quote(title)
        encoded_content = urllib.parse.quote(content)
        
        url = f"https://api.day.app/{BARK_KEY}/{encoded_title}/{encoded_content}"
        print(f"请求URL: https://api.day.app/{BARK_KEY[:10]}.../{encoded_title}/{encoded_content}")
        
        response = requests.get(url, timeout=10)
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✅ Bark推送测试成功！")
            return True
        else:
            print(f"❌ Bark推送测试失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Bark推送测试异常: {e}")
        return False

if __name__ == "__main__":
    test_bark_notification()