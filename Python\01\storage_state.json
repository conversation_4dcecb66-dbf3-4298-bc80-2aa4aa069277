{"cookies": [{"name": "PHPSESSID", "value": "600e0d1099e31e472c35fea0002fcac0", "domain": "checkin.leaflow.net", "path": "/", "expires": -1, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_pk_id.6.40d8", "value": "409dd21557a8d5b2.1755833676.", "domain": "leaflow.net", "path": "/", "expires": 1789788876, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "_pk_ses.6.40d8", "value": "1", "domain": "leaflow.net", "path": "/", "expires": 1755835476, "httpOnly": false, "secure": false, "sameSite": "Lax"}, {"name": "shared_api_cookie", "value": "eyJpdiI6IkE3cllqWGsxZVVHY29TeVRKR2NZV3c9PSIsInZhbHVlIjoiUnYvdUJJQm5IdEc2ejMwMVAyMWcyM1dqd2RRRmFpWTJiU0Z1aVZEZmhmUE1pNU5TOU1TdzZDMXBYQktpbFU2VERIbWtSTDhDVjYzTFhYUUhKVGV1bVczZWNFekFSUVg4RjBlZWZBb1l4TlpadFp6SjYwTTIxQzcxSzVYVEV1QXZrU2EwNVpZUGdNVHdpMXk2WG8vRVpCd0JEdGZBY2dUTUNHTVM3Zkt2TDVoeFhZMURTV2lIUDVkR0syNW9DQmt2ZWlmUVBXRGQvMzc3QlBWMWNiVTBUME11UXZjMFkrYW1yU2Jra2lLVCtqejczQUdRSzIzV0dlUERZSHc1cXBBdlVEbTQ4MHJzcnVSYnJ3SDJWd210aTExZ2lqT2t2QVJsL2hEVzdKSml3WlNXTURKV3luSSt3NFRpcE1LRHVuSkQiLCJtYWMiOiI5YmFlYmNiMjRjNWQ4ZThlYjIyNTdhYzRjMTNhYTM3M2EwZDU2NTE5N2ViYzdkZjhiYmM4ZTBkYmM0NTk2ZmJkIiwidGFnIjoiIn0%3D", "domain": "leaflow.net", "path": "/", "expires": 1755840877.426956, "httpOnly": true, "secure": true, "sameSite": "Lax"}, {"name": "XSRF-TOKEN", "value": "eyJpdiI6ImJ2MnJ1TTNEVEh6cTk4d3lhd1drUXc9PSIsInZhbHVlIjoiZ21rSzY3bTlwM01ZQ25TM3dMamVSUDFhazdpSElwRXpXS1BPOVhzemtwMXlVY2t6d0VsbmZRMW13VGdTc0ZlYTMwZm11M0ZjaDk5ZVE0b2lXVlRpNDJ6VDVXMXZSM1FPcWh1SW1qVlNxM3gzektOUWwvSk1Rb0JYOGtGYkgwbloiLCJtYWMiOiJmZTlhMWM2YjJkZWUzYzhkNDdmMGJmZmUyYWFlMzA2ZDQ5YjNiZTQ1MWRjYWEyZTQzNjY3OGViOTg4ZDJhMmYxIiwidGFnIjoiIn0%3D", "domain": "leaflow.net", "path": "/", "expires": 1755840877.42701, "httpOnly": false, "secure": true, "sameSite": "Lax"}, {"name": "leaflow_session", "value": "eyJpdiI6InpuMFUweWJKYW9wMnRvRjNsMG5zTlE9PSIsInZhbHVlIjoicWQrdFhWMHI3bms3L3NFbFZGK1VlR0hPY3dHTXZKc3NUSmdLQzJJWDRzSUFONnNDWFpLZENsaklYZzBvYW1vNS8yT0RFY2VvZ2dZb1c1d244eG10VUtBbXI3azd5QVZrQlgxbVlXeFBwMGFOZmpkbUIvL0xPa0EyUmdXNTdJRGgiLCJtYWMiOiIwMjlmYjM1Nzk3NmRkNTgzZjc3YWM5ZWYxZTM2MTc5OTMwNmMyNWJmYmNkNGUwYjcwNTBkMjU4YzJkMDc5ZDI2IiwidGFnIjoiIn0%3D", "domain": "leaflow.net", "path": "/", "expires": 1755840877.427029, "httpOnly": true, "secure": true, "sameSite": "Lax"}], "origins": [{"origin": "https://leaflow.net", "localStorage": [{"name": "pusherTransportTLS", "value": "{\"timestamp\":1755833672140,\"transport\":\"ws\",\"latency\":305,\"cacheSkipCount\":0}"}]}, {"origin": "https://recaptcha.net", "localStorage": [{"name": "rc::a", "value": "NjJndTRwdnlmcTB5"}]}]}