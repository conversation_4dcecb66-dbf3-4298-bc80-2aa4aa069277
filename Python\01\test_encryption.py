#!/usr/bin/env python3
"""
测试加密功能
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(__file__))

try:
    from checkin import encrypt_password, decrypt_password
except ImportError:
    # 如果无法导入，直接从当前文件导入函数
    import base64
    import hashlib
    from cryptography.fernet import <PERSON><PERSON><PERSON>
    
    def generate_key_from_password(password: str) -> bytes:
        """从运行密码生成加密密钥"""
        key_hash = hashlib.sha256(password.encode()).digest()
        return base64.urlsafe_b64encode(key_hash)

    def encrypt_password(password: str, run_password: str) -> str:
        """使用运行密码加密账号密码"""
        key = generate_key_from_password(run_password)
        fernet = Fernet(key)
        encrypted = fernet.encrypt(password.encode())
        return base64.urlsafe_b64encode(encrypted).decode()

    def decrypt_password(encrypted_password: str, run_password: str) -> str:
        """使用运行密码解密账号密码"""
        try:
            key = generate_key_from_password(run_password)
            fernet = Fernet(key)
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_password.encode())
            decrypted = fernet.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            raise ValueError(f"密码解密失败，请检查运行密码是否正确: {e}")

def test_encryption():
    print("=== 测试密码加密/解密功能 ===")
    
    # 测试数据
    original_password = "test_password_123"
    run_password = "my_run_password"
    
    print(f"原始密码: {original_password}")
    print(f"运行密码: {run_password}")
    
    # 加密
    try:
        encrypted = encrypt_password(original_password, run_password)
        print(f"加密后: {encrypted}")
    except Exception as e:
        print(f"加密失败: {e}")
        return False
    
    # 解密
    try:
        decrypted = decrypt_password(encrypted, run_password)
        print(f"解密后: {decrypted}")
    except Exception as e:
        print(f"解密失败: {e}")
        return False
    
    # 验证
    if original_password == decrypted:
        print("✅ 加密/解密测试成功！")
        return True
    else:
        print("❌ 加密/解密测试失败！")
        return False

def test_wrong_password():
    print("\n=== 测试错误运行密码 ===")
    
    original_password = "test_password_123"
    run_password = "correct_password"
    wrong_password = "wrong_password"
    
    # 加密
    encrypted = encrypt_password(original_password, run_password)
    
    # 用错误密码解密
    try:
        decrypted = decrypt_password(encrypted, wrong_password)
        print("❌ 应该解密失败但成功了！")
        return False
    except Exception as e:
        print(f"✅ 正确检测到错误密码: {e}")
        return True

if __name__ == "__main__":
    print("开始测试加密功能...")
    
    success1 = test_encryption()
    success2 = test_wrong_password()
    
    if success1 and success2:
        print("\n🎉 所有测试通过！")
    else:
        print("\n❌ 测试失败！")