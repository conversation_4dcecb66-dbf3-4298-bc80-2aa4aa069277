import asyncio
import sys
from playwright.async_api import async_playwright

async def login_and_checkin(username, password):
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # headless=False 可视化调试
        page = await browser.new_page()

        # 1. 打开登录页
        await page.goto("https://leaflow.net/login")

        # 2. 填写账号密码并登录
        await page.fill('input[name="username"]', username)  # 根据实际HTML修改选择器
        await page.fill('input[name="password"]', password)   # 根据实际HTML修改选择器
        await page.click('button[type="submit"]')             # 登录按钮

        # 3. 等待跳转至 dashboard 页面
        await page.wait_for_url("https://leaflow.net/dashboard", timeout=60000)

        # 4. 点击签到按钮 (根据你提供的xpath)
        await page.click('/html/body/div[1]/div[1]/main/div/div[1]/div/div[2]/button[9]')

        # 5. 等待跳转至授权页面
        await page.wait_for_url("**/oauth/authorize**", timeout=60000)

        # 6. 点击授权按钮
        await page.click('/html/body/div/div/div[2]/div[2]/button[1]')

        # 7. 等待跳转到签到页面 checkin.leaflow.net
        await page.wait_for_url("https://checkin.leaflow.net/", timeout=60000)

        # 8. 点击签到按钮
        await page.click('/html/body/div/div/div[1]/div/div[3]/button')

        # 9. 等待签到完成并获取结果
        await page.wait_for_selector(".checkin-card", timeout=30000)

        # 提取签到信息
        result_text = await page.inner_text(".checkin-card")
        print("签到结果：")
        print(result_text.strip())

        # 关闭浏览器
        await browser.close()


if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("请提供账号和密码作为命令行参数：")
        print("示例: python checkin.py your_username your_password")
        sys.exit(1)

    username = sys.argv[1]
    password = sys.argv[2]

    asyncio.run(login_and_checkin(username, password))
