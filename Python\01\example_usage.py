#!/usr/bin/env python3
"""
Leaflow签到工具使用示例
"""

import subprocess
import sys
import os

def run_command(cmd):
    """运行命令并显示输出"""
    print(f"\n>>> {cmd}")
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, encoding='utf-8')
        if result.stdout:
            print(result.stdout)
        if result.stderr:
            print(f"错误: {result.stderr}")
        return result.returncode == 0
    except Exception as e:
        print(f"执行命令失败: {e}")
        return False

def main():
    print("=" * 60)
    print("🚀 Leaflow签到工具使用示例")
    print("=" * 60)
    
    script_path = "Python/01/07-checkin.py"
    
    print("\n1. 查看帮助信息")
    run_command(f"python {script_path} --help")
    
    print("\n2. 列出当前用户（应该为空）")
    run_command(f"python {script_path} --list-users")
    
    print("\n3. 添加用户示例")
    print("注意: 这只是演示命令，实际使用时需要交互式输入")
    print(f"python {script_path} --add-user")
    print("然后按提示输入:")
    print("- 用户名: <EMAIL>")
    print("- 密码: your_password")
    print("- 运行密码: my_run_password")
    print("- Bark Key: (可选)")
    
    print("\n4. 签到命令示例")
    print("签到所有用户:")
    print(f"python {script_path} my_run_password")
    print("\n签到指定用户:")
    print(f"python {script_path} my_run_password --username <EMAIL>")
    print("\n调试模式:")
    print(f"python {script_path} my_run_password --headful --debug")
    
    print("\n5. 用户管理示例")
    print("删除用户:")
    print(f"python {script_path} --remove-user <EMAIL>")
    
    print("\n6. 兼容旧版本")
    print("直接使用用户名密码:")
    print(f"python {script_path} --old-username <EMAIL> --old-password your_password")
    
    print("\n" + "=" * 60)
    print("📖 详细使用说明请查看 README_CHECKIN.md")
    print("🔧 安装依赖请运行: python install_checkin_deps.py")
    print("=" * 60)

if __name__ == "__main__":
    main()