# Leaflow 多用户自动签到工具

## 功能特点

- 🔐 **密码加密存储**: 使用运行密码对账号密码进行加密保存，确保安全
- 👥 **多用户支持**: 支持管理多个账号，批量签到
- 📱 **Bark通知**: 支持每个用户独立的Bark通知配置
- 🛡️ **安全设计**: 密码不明文存储，需要运行密码才能解密使用
- 📊 **详细日志**: 完整的签到流程记录和结果统计

## 安装依赖

```bash
python install_checkin_deps.py
```

## 使用方法

### 1. 添加用户

```bash
python 07-checkin.py --add-user
```

按提示输入：
- 用户名（邮箱）
- 密码
- 运行密码（用于加密保存，请牢记）
- Bark Key（可选）

### 2. 管理用户

```bash
# 列出所有用户
python 07-checkin.py --list-users

# 删除用户
python 07-checkin.py --remove-user <用户名>
```

### 3. 执行签到

```bash
# 为所有用户签到
python 07-checkin.py <运行密码>

# 为指定用户签到
python 07-checkin.py <运行密码> --username <用户名>

# 调试模式（显示浏览器界面）
python 07-checkin.py <运行密码> --headful --debug
```

## 安全说明

### 密码加密机制

1. **运行密码**: 你设置的主密码，用于加密/解密所有账号密码
2. **加密算法**: 使用 AES-256 加密算法（Fernet）
3. **密钥生成**: 运行密码通过 SHA-256 哈希生成加密密钥
4. **存储安全**: 配置文件中只保存加密后的密码，无法直接读取

### 配置文件结构

```json
{
  "users": [
    {
      "username": "<EMAIL>",
      "encrypted_password": "加密后的密码",
      "bark_key": "Bark通知Key",
      "created_at": 1640995200,
      "last_checkin": 1640995200
    }
  ],
  "version": "1.0"
}
```

## 命令行参数

### 用户管理
- `--add-user`: 添加新用户
- `--remove-user <用户名>`: 删除指定用户
- `--list-users`: 列出所有用户

### 签到参数
- `<运行密码>`: 必需，用于解密账号密码
- `--username <用户名>`: 可选，指定签到用户
- `--headful`: 显示浏览器界面（调试用）
- `--debug`: 输出详细调试信息

### 兼容参数（旧版本）
- `--old-username`: 直接指定用户名
- `--old-password`: 直接指定密码
- `--bark-key`: 直接指定Bark Key

## 使用示例

### 初次使用

```bash
# 1. 安装依赖
python install_checkin_deps.py

# 2. 添加第一个用户
python 07-checkin.py --add-user
# 输入: <EMAIL>, password123, myrunpass, BarkKey123

# 3. 添加第二个用户
python 07-checkin.py --add-user
# 输入: <EMAIL>, password456, myrunpass, BarkKey456

# 4. 查看用户列表
python 07-checkin.py --list-users

# 5. 为所有用户签到
python 07-checkin.py myrunpass
```

### 日常使用

```bash
# 每日签到所有用户
python 07-checkin.py myrunpass

# 只为特定用户签到
python 07-checkin.py myrunpass --username <EMAIL>

# 调试模式查看问题
python 07-checkin.py myrunpass --headful --debug
```

## 定时任务设置

### Windows 任务计划程序

1. 打开"任务计划程序"
2. 创建基本任务
3. 设置每日执行
4. 操作：启动程序
5. 程序：`python`
6. 参数：`C:\path\to\07-checkin.py myrunpass`

### Linux/macOS Cron

```bash
# 编辑crontab
crontab -e

# 添加每日9点执行
0 9 * * * cd /path/to/script && python 07-checkin.py myrunpass
```

## 注意事项

1. **运行密码安全**: 请妥善保管运行密码，忘记后无法恢复账号密码
2. **网络环境**: 确保网络连接稳定，可访问目标网站
3. **浏览器依赖**: 需要安装 Chromium 浏览器（自动安装）
4. **文件权限**: 确保配置文件有读写权限
5. **备份配置**: 建议定期备份 `users_config.json` 文件

## 故障排除

### 常见问题

1. **密码解密失败**
   - 检查运行密码是否正确
   - 确认配置文件未损坏

2. **登录失败**
   - 检查账号密码是否正确
   - 确认网站是否正常访问

3. **签到失败**
   - 使用 `--headful --debug` 查看详细过程
   - 检查网站页面是否有变化

4. **Bark通知失败**
   - 检查Bark Key是否正确
   - 确认网络可访问Bark服务

### 日志文件

- `checkin_result.json`: 最后一次签到结果
- `checkin_result.png`: 最后一次签到截图

## 更新日志

### v2.0.0
- 新增多用户支持
- 新增密码加密存储
- 新增用户管理功能
- 新增独立Bark通知配置
- 优化签到流程和错误处理

### v1.0.0
- 基础签到功能
- 单用户支持
- Bark通知支持