#!/usr/bin/env python3
"""
Leaflow签到工具依赖安装脚本
"""

import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def check_package(package):
    """检查包是否已安装"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    print("=" * 60)
    print("🚀 Leaflow签到工具依赖安装器")
    print("=" * 60)
    
    # 需要安装的包
    packages = [
        ("requests", "requests"),
        ("playwright", "playwright"),
        ("beautifulsoup4", "bs4"),
        ("cryptography", "cryptography")
    ]
    
    print("📋 检查依赖包...")
    
    need_install = []
    for pip_name, import_name in packages:
        if check_package(import_name):
            print(f"✅ {pip_name} 已安装")
        else:
            print(f"❌ {pip_name} 未安装")
            need_install.append(pip_name)
    
    if not need_install:
        print("\n🎉 所有依赖包都已安装！")
    else:
        print(f"\n📦 需要安装 {len(need_install)} 个包...")
        
        for package in need_install:
            if not install_package(package):
                print(f"\n❌ 安装失败，请手动安装: pip install {package}")
                return False
    
    # 安装playwright浏览器
    print("\n🌐 安装Playwright浏览器...")
    try:
        subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
        print("✅ Playwright浏览器安装成功")
    except subprocess.CalledProcessError as e:
        print(f"❌ Playwright浏览器安装失败: {e}")
        print("请手动运行: playwright install chromium")
        return False
    
    print("\n" + "=" * 60)
    print("🎉 所有依赖安装完成！")
    print("=" * 60)
    print("📖 使用说明:")
    print("1. 添加用户: python 07-checkin.py --add-user")
    print("2. 列出用户: python 07-checkin.py --list-users")
    print("3. 签到所有用户: python 07-checkin.py <运行密码>")
    print("4. 签到指定用户: python 07-checkin.py <运行密码> --username <用户名>")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️ 安装被用户中断")
    except Exception as e:
        print(f"\n❌ 安装过程中出现错误: {e}")