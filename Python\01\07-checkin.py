import os, re, json, time, argparse, requests, base64, hashlib
from pathlib import Path
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any, List
from playwright.sync_api import sync_playwright
from bs4 import BeautifulSoup
from cryptography.fernet import Fernet

# URL配置
LOGIN_URL = "https://leaflow.net/launchpad"
DASHBOARD_URL = "https://leaflow.net/launchpad"
CHECKIN_PAGE_URL = "https://checkin.leaflow.net/"
OAUTH_URL_PATTERN = "https://leaflow.net/oauth/authorize"

# 文件配置
RESULT_SCREENSHOT = "checkin_result.png"
RESULT_JSON = "checkin_result.json"
USERS_CONFIG_FILE = "users_config.json"

# Bark通知配置
BARK_KEY = "AhMyXTSspmTD3xeu2SpMqX"  # 请在这里填写你的Bark Key

AUTH_TEXTS = ["授权", "同意", "允许", "Authorize", "Confirm", "Allow"]
LOGIN_BUTTON_TEXTS = ["登录", "登入", "Login", "Sign in", "Sign In"]
CHECKIN_BUTTON_TEXTS = ["签到", "打卡", "Check in", "Check-in", "Checkin", "Clock in", "Punch"]
CHECKIN_URL_KEYWORDS = ["checkin", "sign", "attendance", "punch", "clock", "qiandao"]

def mask(s: str) -> str:
    if not s: return ""
    return s[:2] + "****" + s[-2:]

def generate_key_from_password(password: str) -> bytes:
    """从运行密码生成加密密钥"""
    # 使用SHA256哈希生成固定长度的密钥
    key_hash = hashlib.sha256(password.encode()).digest()
    # 转换为Fernet需要的base64格式
    return base64.urlsafe_b64encode(key_hash)

def encrypt_password(password: str, run_password: str) -> str:
    """使用运行密码加密账号密码"""
    key = generate_key_from_password(run_password)
    fernet = Fernet(key)
    encrypted = fernet.encrypt(password.encode())
    return base64.urlsafe_b64encode(encrypted).decode()

def decrypt_password(encrypted_password: str, run_password: str) -> str:
    """使用运行密码解密账号密码"""
    try:
        key = generate_key_from_password(run_password)
        fernet = Fernet(key)
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_password.encode())
        decrypted = fernet.decrypt(encrypted_bytes)
        return decrypted.decode()
    except Exception as e:
        raise ValueError(f"密码解密失败，请检查运行密码是否正确: {e}")

def load_users_config() -> Dict[str, Any]:
    """加载用户配置文件"""
    if not os.path.exists(USERS_CONFIG_FILE):
        return {"users": [], "version": "1.0"}
    
    try:
        with open(USERS_CONFIG_FILE, "r", encoding="utf-8") as f:
            return json.load(f)
    except Exception as e:
        print(f"[ERROR] 加载用户配置失败: {e}")
        return {"users": [], "version": "1.0"}

def save_users_config(config: Dict[str, Any]) -> bool:
    """保存用户配置文件"""
    try:
        with open(USERS_CONFIG_FILE, "w", encoding="utf-8") as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"[ERROR] 保存用户配置失败: {e}")
        return False

def add_user(username: str, password: str, run_password: str, bark_key: str = "") -> bool:
    """添加新用户"""
    config = load_users_config()
    
    # 检查用户是否已存在
    for user in config["users"]:
        if user["username"] == username:
            print(f"[ERROR] 用户 {mask(username)} 已存在")
            return False
    
    # 加密密码
    try:
        encrypted_password = encrypt_password(password, run_password)
    except Exception as e:
        print(f"[ERROR] 密码加密失败: {e}")
        return False
    
    # 添加用户
    user_data = {
        "username": username,
        "encrypted_password": encrypted_password,
        "bark_key": bark_key,
        "created_at": int(time.time()),
        "last_checkin": 0
    }
    
    config["users"].append(user_data)
    
    if save_users_config(config):
        print(f"[SUCCESS] 用户 {mask(username)} 添加成功")
        return True
    else:
        return False

def remove_user(username: str) -> bool:
    """删除用户"""
    config = load_users_config()
    
    original_count = len(config["users"])
    config["users"] = [user for user in config["users"] if user["username"] != username]
    
    if len(config["users"]) < original_count:
        if save_users_config(config):
            print(f"[SUCCESS] 用户 {mask(username)} 删除成功")
            return True
    else:
        print(f"[ERROR] 用户 {mask(username)} 不存在")
        return False

def list_users() -> None:
    """列出所有用户"""
    config = load_users_config()
    
    if not config["users"]:
        print("[INFO] 暂无用户配置")
        return
    
    print(f"[INFO] 共有 {len(config['users'])} 个用户:")
    print("-" * 60)
    for i, user in enumerate(config["users"], 1):
        last_checkin = "从未签到" if user["last_checkin"] == 0 else time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(user["last_checkin"]))
        created_at = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(user["created_at"]))
        bark_status = "已配置" if user.get("bark_key") else "未配置"
        
        print(f"{i}. 用户名: {mask(user['username'])}")
        print(f"   Bark通知: {bark_status}")
        print(f"   创建时间: {created_at}")
        print(f"   最后签到: {last_checkin}")
        print("-" * 60)

def get_user_credentials(username: str, run_password: str) -> Optional[Tuple[str, str, str]]:
    """获取用户凭据（解密后的密码和Bark Key）"""
    config = load_users_config()
    
    for user in config["users"]:
        if user["username"] == username:
            try:
                decrypted_password = decrypt_password(user["encrypted_password"], run_password)
                bark_key = user.get("bark_key", "")
                return username, decrypted_password, bark_key
            except Exception as e:
                print(f"[ERROR] 解密用户 {mask(username)} 的密码失败: {e}")
                return None
    
    print(f"[ERROR] 用户 {mask(username)} 不存在")
    return None

def update_user_last_checkin(username: str) -> None:
    """更新用户最后签到时间"""
    config = load_users_config()
    
    for user in config["users"]:
        if user["username"] == username:
            user["last_checkin"] = int(time.time())
            save_users_config(config)
            break

def try_click_text(page, texts, timeout=4000) -> bool:
    for t in texts:
        pat = re.compile(re.escape(t), re.I)
        # 优先 ARIA role
        try:
            page.get_by_role("button", name=pat).first.wait_for(state="visible", timeout=timeout)
            page.get_by_role("button", name=pat).first.click()
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
        except Exception:
            pass
        # 回退文本选择
        try:
            page.locator(f"text={t}").first.wait_for(state="visible", timeout=timeout//2)
            page.locator(f"text={t}").first.click()
            page.wait_for_load_state("networkidle", timeout=8000)
            return True
        except Exception:
            pass
    return False

def click_authorize_button(page, timeout=5000, debug=False) -> bool:
    """点击授权按钮，使用精确的选择器"""
    
    if debug:
        # 调试模式：输出页面信息
        try:
            print(f"[DEBUG] 当前页面URL: {page.url}")
            print(f"[DEBUG] 页面标题: {page.title()}")
            
            # 查找所有按钮
            buttons = page.locator("button").all()
            print(f"[DEBUG] 页面中找到 {len(buttons)} 个按钮:")
            for i, btn in enumerate(buttons):
                try:
                    btn_text = btn.inner_text()
                    btn_id = btn.get_attribute("id") or "无ID"
                    btn_class = btn.get_attribute("class") or "无class"
                    btn_onclick = btn.get_attribute("onclick") or "无onclick"
                    print(f"[DEBUG] 按钮{i+1}: 文本='{btn_text}', ID='{btn_id}', class='{btn_class}', onclick='{btn_onclick}'")
                except Exception:
                    print(f"[DEBUG] 按钮{i+1}: 无法获取信息")
        except Exception as e:
            print(f"[DEBUG] 调试信息获取失败: {e}")
    
    try:
        # 方法1：使用ID选择器
        approve_btn = page.locator("#approve-btn")
        if approve_btn.count() > 0:
            approve_btn.wait_for(state="visible", timeout=timeout)
            approve_btn.click()
            print("[AUTH] 通过ID选择器点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] ID选择器失败: {e}")
    
    try:
        # 方法2：使用class选择器
        approve_btn = page.locator(".btn-approve")
        if approve_btn.count() > 0:
            approve_btn.wait_for(state="visible", timeout=timeout)
            approve_btn.click()
            print("[AUTH] 通过class选择器点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] class选择器失败: {e}")
    
    try:
        # 方法3：使用onclick属性选择器
        approve_btn = page.locator("button[onclick='accept()']")
        if approve_btn.count() > 0:
            approve_btn.wait_for(state="visible", timeout=timeout)
            approve_btn.click()
            print("[AUTH] 通过onclick选择器点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] onclick选择器失败: {e}")
    
    try:
        # 方法4：使用复合选择器
        approve_btn = page.locator("button.btn.btn-approve#approve-btn")
        if approve_btn.count() > 0:
            approve_btn.wait_for(state="visible", timeout=timeout)
            approve_btn.click()
            print("[AUTH] 通过复合选择器点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] 复合选择器失败: {e}")
    
    try:
        # 方法5：通过文本内容查找
        approve_btn = page.locator("button:has-text('授权')")
        if approve_btn.count() > 0:
            approve_btn.wait_for(state="visible", timeout=timeout)
            approve_btn.click()
            print("[AUTH] 通过文本内容点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] 文本选择器失败: {e}")
    
    try:
        # 方法6：JavaScript执行点击
        js_result = page.evaluate("""
            () => {
                const btn = document.getElementById('approve-btn');
                if (btn) {
                    btn.click();
                    return true;
                }
                return false;
            }
        """)
        if js_result:
            print("[AUTH] 通过JavaScript点击授权按钮成功")
            page.wait_for_load_state("networkidle", timeout=10000)
            return True
    except Exception as e:
        print(f"[AUTH] JavaScript点击失败: {e}")
    
    print("[AUTH] 所有授权按钮选择器都失败了")
    return False

def has_authorize_button(page) -> bool:
    """检查页面是否有授权按钮"""
    try:
        # 检查多种授权按钮的存在
        selectors = [
            "#approve-btn",
            ".btn-approve", 
            "button[onclick='accept()']",
            "button:has-text('授权')"
        ]
        
        for selector in selectors:
            if page.locator(selector).count() > 0:
                return True
        return False
    except Exception:
        return False

def click_authorize_anywhere(context, timeout_each=2500, debug=False) -> bool:
    """在所有页面中查找并点击授权按钮"""
    ok = False
    
    # 首先检查是否真的有授权按钮
    auth_page_found = False
    for i, p in enumerate(context.pages):
        if has_authorize_button(p):
            auth_page_found = True
            print(f"[AUTH] 在页面 {i+1} 发现授权按钮: {p.url}")
            break
    
    if not auth_page_found:
        print("[AUTH] 未发现授权按钮，跳过授权步骤")
        return False
    
    # 检查当前所有页面
    for i, p in enumerate(context.pages):
        try:
            if has_authorize_button(p):
                print(f"[AUTH] 尝试在页面 {i+1} 点击授权按钮")
                if click_authorize_button(p, timeout=timeout_each, debug=debug):
                    ok = True
                    break
        except Exception as e:
            print(f"[AUTH] 页面{i+1}授权失败: {e}")
            continue
    
    # 如果没有成功，等待可能的弹窗
    if not ok:
        try:
            print("[AUTH] 等待可能的授权弹窗...")
            popup = context.wait_for_event("page", timeout=1200)
            print(f"[AUTH] 检测到弹窗: {popup.url}")
            if has_authorize_button(popup) and click_authorize_button(popup, timeout=timeout_each, debug=debug):
                ok = True
        except Exception as e:
            print(f"[AUTH] 弹窗授权失败: {e}")
    
    return ok

def find_and_fill_login(page, username, password, timeout_ms=12000) -> bool:
    user_fields = [
        lambda: page.get_by_label(re.compile("邮箱|邮件|Email|E-mail|账号|用户名|User|Phone|手机号", re.I)).first,
        lambda: page.get_by_placeholder(re.compile("邮箱|Email|账号|用户名|User|Phone|手机号", re.I)).first,
        lambda: page.locator("input[type='email'], input[type='text']").filter(has_not=page.locator("[type='hidden']")).first,
        lambda: page.locator("input[name*='user' i], input[name*='email' i], input[name*='phone' i]").first,
    ]
    pass_fields = [
        lambda: page.get_by_label(re.compile("密码|Password", re.I)).first,
        lambda: page.get_by_placeholder(re.compile("密码|Password", re.I)).first,
        lambda: page.locator("input[type='password']").first,
    ]
    login_buttons = [
        lambda: page.get_by_role("button", name=re.compile("|".join(map(re.escape, LOGIN_BUTTON_TEXTS)), re.I)).first,
        lambda: page.locator("button:has-text('登录'), button:has-text('Login'), input[type='submit']").first,
    ]
    # 用户名
    for get in user_fields:
        try:
            u = get()
            u.wait_for(state="visible", timeout=timeout_ms); u.click(); u.fill(username)
            break
        except Exception:
            continue
    else:
        return False
    # 密码
    for get in pass_fields:
        try:
            p = get()
            p.wait_for(state="visible", timeout=timeout_ms); p.click(); p.fill(password)
            break
        except Exception:
            continue
    else:
        return False
    # 登录
    for get in login_buttons:
        try:
            b = get()
            b.wait_for(state="visible", timeout=4000)
            with page.expect_navigation(wait_until="load", timeout=15000):
                b.click()
            return True
        except Exception:
            continue
    # 兜底：回车提交
    try:
        page.keyboard.press("Enter")
        page.wait_for_load_state("networkidle", timeout=15000)
        return True
    except Exception:
        return False

def try_login(page, username, password, timeout_s=30) -> bool:
    """在指定页面尝试登录"""
    try:
        return find_and_fill_login(page, username, password, timeout_ms=timeout_s*1000)
    except Exception as e:
        print(f"[LOGIN] 登录失败: {e}")
        return False

def click_dashboard_checkin_button(page, timeout=5000, debug=False) -> bool:
    """登录后直接跳转到签到页面并处理授权"""
    
    if debug:
        print("[DASHBOARD] 开始处理签到页面跳转...")
    
    try:
        # 直接跳转到签到页面
        print("[DASHBOARD] 跳转到签到页面: https://checkin.leaflow.net")
        page.goto("https://checkin.leaflow.net", wait_until="load")
        time.sleep(3)
        
        current_url = page.url
        print(f"[DASHBOARD] 当前页面URL: {current_url}")
        
        # 检查是否需要OAuth授权
        auth_success = False
        if "oauth" in current_url.lower() or "authorize" in current_url.lower():
            print("[DASHBOARD] 检测到OAuth授权页面，开始处理...")
            
            # 尝试多种方式点击授权按钮
            try:
                # 方法1：通过ID选择器
                auth_btn = page.locator("#authorize")
                if auth_btn.count() > 0:
                    auth_btn.click()
                    print("[DASHBOARD] 通过ID点击授权按钮成功")
                    auth_success = True
                    time.sleep(3)
            except Exception as e:
                print(f"[DASHBOARD] ID选择器失败: {e}")
            
            if not auth_success:
                try:
                    # 方法2：通过class选择器
                    auth_btn = page.locator(".btn-authorize, .authorize-btn, .oauth-authorize")
                    if auth_btn.count() > 0:
                        auth_btn.first.click()
                        print("[DASHBOARD] 通过class点击授权按钮成功")
                        auth_success = True
                        time.sleep(3)
                except Exception as e:
                    print(f"[DASHBOARD] class选择器失败: {e}")
            
            if not auth_success:
                try:
                    # 方法3：通过XPath选择器
                    auth_btn = page.locator("//button[contains(text(), '授权') or contains(text(), 'Authorize') or contains(text(), '同意')]")
                    if auth_btn.count() > 0:
                        auth_btn.first.click()
                        print("[DASHBOARD] 通过XPath点击授权按钮成功")
                        auth_success = True
                        time.sleep(3)
                except Exception as e:
                    print(f"[DASHBOARD] XPath选择器失败: {e}")
            
            if not auth_success:
                try:
                    # 方法4：通过文本内容选择器
                    auth_btn = page.get_by_text("授权").or_(page.get_by_text("Authorize")).or_(page.get_by_text("同意"))
                    if auth_btn.count() > 0:
                        auth_btn.first.click()
                        print("[DASHBOARD] 通过文本点击授权按钮成功")
                        auth_success = True
                        time.sleep(3)
                except Exception as e:
                    print(f"[DASHBOARD] 文本选择器失败: {e}")
            
            if auth_success:
                print("[DASHBOARD] OAuth授权处理成功")
                # 等待授权完成后的页面跳转
                time.sleep(3)
            else:
                print("[DASHBOARD] OAuth授权处理失败，但继续流程")
        
        print("[DASHBOARD] 签到页面跳转成功")
        return True
        
    except Exception as e:
        print(f"[DASHBOARD] 跳转到签到页面失败: {e}")
        return False

def click_oauth_authorize_button(page, timeout=5000, debug=False) -> bool:
    """在OAuth授权页面或嵌入页面中点击授权按钮"""
    
    if debug:
        print(f"[DEBUG] 当前页面URL: {page.url}")
        print(f"[DEBUG] 查找OAuth授权按钮...")
        
        # 输出页面中所有按钮信息
        try:
            buttons = page.locator("button").all()
            print(f"[DEBUG] 页面中找到 {len(buttons)} 个按钮:")
            for i, btn in enumerate(buttons):
                try:
                    btn_text = btn.inner_text().strip()
                    btn_class = btn.get_attribute("class") or "无class"
                    if "授权" in btn_text or "authorize" in btn_text.lower():
                        print(f"[DEBUG] 找到授权相关按钮{i+1}: 文本='{btn_text}', class='{btn_class}'")
                except Exception:
                    pass
        except Exception as e:
            print(f"[DEBUG] 调试信息获取失败: {e}")
    
    try:
        # 方法1：使用提供的XPath（适用于独立OAuth页面）
        xpath_btn = page.locator("xpath=/html/body/div/div/div[2]/div[2]/button[1]")
        if xpath_btn.count() > 0:
            xpath_btn.wait_for(state="visible", timeout=timeout)
            xpath_btn.click()
            print("[OAUTH] 通过XPath点击授权按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[OAUTH] XPath选择器失败: {e}")
    
    try:
        # 方法2：通过文本内容查找授权按钮
        auth_btn = page.locator("button:has-text('授权')")
        if auth_btn.count() > 0:
            auth_btn.first.wait_for(state="visible", timeout=timeout)
            auth_btn.first.click()
            print("[OAUTH] 通过文本内容点击授权按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[OAUTH] 文本选择器失败: {e}")
    
    try:
        # 方法3：查找包含"Authorize"的按钮
        auth_btn = page.locator("button:has-text('Authorize')")
        if auth_btn.count() > 0:
            auth_btn.first.wait_for(state="visible", timeout=timeout)
            auth_btn.first.click()
            print("[OAUTH] 通过Authorize文本点击授权按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[OAUTH] Authorize文本选择器失败: {e}")
    
    try:
        # 方法4：通过常见的授权按钮class
        auth_btn = page.locator("button.btn-primary, button.btn-success, button.btn-approve")
        if auth_btn.count() > 0:
            # 检查按钮文本是否包含授权相关内容
            for btn in auth_btn.all():
                try:
                    btn_text = btn.inner_text().strip()
                    if "授权" in btn_text or "authorize" in btn_text.lower() or "同意" in btn_text or "允许" in btn_text:
                        btn.wait_for(state="visible", timeout=timeout)
                        btn.click()
                        print(f"[OAUTH] 通过class选择器点击授权按钮成功: {btn_text}")
                        time.sleep(2)
                        return True
                except Exception:
                    continue
    except Exception as e:
        print(f"[OAUTH] class选择器失败: {e}")
    
    try:
        # 方法5：JavaScript执行点击
        js_result = page.evaluate("""
            () => {
                const buttons = document.querySelectorAll('button');
                for (let btn of buttons) {
                    const text = btn.textContent || btn.innerText || '';
                    if (text.includes('授权') || text.toLowerCase().includes('authorize') || 
                        text.includes('同意') || text.includes('允许')) {
                        btn.click();
                        return true;
                    }
                }
                return false;
            }
        """)
        if js_result:
            print("[OAUTH] 通过JavaScript点击授权按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[OAUTH] JavaScript点击失败: {e}")
    
    print("[OAUTH] 所有授权按钮选择器都失败了")
    return False

def click_final_checkin_button(page, timeout=5000, debug=False) -> bool:
    """在最终签到页面点击签到按钮"""
    
    if debug:
        print(f"[DEBUG] 当前页面URL: {page.url}")
        print(f"[DEBUG] 查找最终签到按钮...")
    
    try:
        # 方法1：使用提供的XPath
        xpath_btn = page.locator("xpath=/html/body/div/div/div[1]/div/form/button")
        if xpath_btn.count() > 0:
            xpath_btn.wait_for(state="visible", timeout=timeout)
            xpath_btn.click()
            print("[FINAL] 通过XPath点击签到按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[FINAL] XPath选择器失败: {e}")
    
    try:
        # 方法2：通过文本内容查找签到按钮
        checkin_btn = page.locator("button:has-text('签到')")
        if checkin_btn.count() > 0:
            checkin_btn.first.wait_for(state="visible", timeout=timeout)
            checkin_btn.first.click()
            print("[FINAL] 通过文本内容点击签到按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[FINAL] 文本选择器失败: {e}")
    
    try:
        # 方法3：JavaScript执行点击
        js_result = page.evaluate("""
            () => {
                const buttons = document.querySelectorAll('button');
                for (let btn of buttons) {
                    if (btn.textContent && btn.textContent.includes('签到') && !btn.disabled) {
                        btn.click();
                        return true;
                    }
                }
                return false;
            }
        """)
        if js_result:
            print("[FINAL] 通过JavaScript点击签到按钮成功")
            time.sleep(2)
            return True
    except Exception as e:
        print(f"[FINAL] JavaScript点击失败: {e}")
    
    print("[FINAL] 所有签到按钮选择器都失败了")
    return False

def click_checkin(page) -> bool:
    """执行签到操作，不依赖网络请求监听"""
    try:
        return click_checkin_button(page, timeout=6000, debug=False)
    except Exception as e:
        print(f"[CHECKIN] 签到失败: {e}")
        return False

def wait_checkin_response(page, timeout=10000) -> Optional[Tuple[int, str, str]]:
    def matcher(resp): return any(k in resp.url.lower() for k in CHECKIN_URL_KEYWORDS)
    try:
        with page.expect_response(matcher, timeout=timeout) as ri: pass
        r = ri.value
        try: body = r.text()
        except Exception: body = "<non-text body>"
        return r.status, r.url, body[:2000]
    except Exception:
        return None

def parse_checkin_result_from_iframe(page) -> Dict[str, Any]:
    """从iframe中解析签到结果页面，提取连续签到天数和今日奖励金额"""
    result = {
        "consecutive_days": 0,
        "today_reward": "0",
        "already_checked": False,
        "success": False
    }
    
    try:
        # 尝试从iframe中获取内容
        iframe_content = ""
        try:
            iframe_element = page.frame_locator("iframe").first
            iframe_content = iframe_element.locator("body").inner_html()
            print(f"[DEBUG] 成功获取iframe内容，长度: {len(iframe_content)}")
        except Exception as e:
            print(f"[DEBUG] 获取iframe内容失败，尝试从主页面获取: {e}")
            # 回退到主页面内容
            iframe_content = page.content()
        
        soup = BeautifulSoup(iframe_content, 'html.parser')
        
        print(f"[DEBUG] 开始解析iframe内容...")
        
        # 查找连续签到天数 - 多种方式
        # 方式1：通过class='streak-badge'
        streak_badge = soup.find('div', class_='streak-badge')
        if streak_badge:
            streak_text = streak_badge.get_text()
            print(f"[DEBUG] 找到streak-badge: {streak_text}")
            days_match = re.search(r'已连续签到\s*(\d+)\s*天', streak_text)
            if days_match:
                result["consecutive_days"] = int(days_match.group(1))
                print(f"[DEBUG] 解析到连续签到天数: {result['consecutive_days']}")
        
        # 方式2：直接在HTML中搜索连续签到信息
        if result["consecutive_days"] == 0:
            days_match = re.search(r'已连续签到\s*(\d+)\s*天', iframe_content)
            if days_match:
                result["consecutive_days"] = int(days_match.group(1))
                print(f"[DEBUG] 通过HTML搜索解析到连续签到天数: {result['consecutive_days']}")
        
        # 查找今日奖励金额 - 多种方式
        # 方式1：通过class='reward-amount'
        reward_amount = soup.find('div', class_='reward-amount')
        if reward_amount:
            reward_text = reward_amount.get_text().strip()
            print(f"[DEBUG] 找到reward-amount: {reward_text}")
            amount_match = re.search(r'([\d.]+)\s*元', reward_text)
            if amount_match:
                result["today_reward"] = amount_match.group(1)
                print(f"[DEBUG] 解析到奖励金额: {result['today_reward']}")
        
        # 方式2：直接在HTML中搜索奖励金额
        if result["today_reward"] == "0":
            # 查找类似 "0.34 元" 的模式
            amount_matches = re.findall(r'(\d+\.\d+)\s*元', iframe_content)
            if amount_matches:
                # 取第一个找到的金额（通常是今日奖励）
                result["today_reward"] = amount_matches[0]
                print(f"[DEBUG] 通过HTML搜索解析到奖励金额: {result['today_reward']}")
        
        # 检查是否已签到 - 多种方式
        # 方式1：检查按钮状态
        checkin_btn = soup.find('button', class_='checkin-btn')
        if checkin_btn and checkin_btn.get('disabled') is not None:
            result["already_checked"] = True
            print(f"[DEBUG] 通过按钮disabled状态检测到已签到")
        
        # 方式2：检查文本内容
        already_signed_texts = [
            "今日已签到，获得奖励",
            "今日已签到", 
            "明天再来继续领取奖励吧",
            "已签到"
        ]
        
        for text in already_signed_texts:
            if text in iframe_content:
                result["already_checked"] = True
                result["success"] = True
                print(f"[DEBUG] 通过文本'{text}'检测到已签到")
                break
        
        # 方式3：检查按钮文本
        buttons = soup.find_all('button')
        for btn in buttons:
            btn_text = btn.get_text().strip()
            if "已签到" in btn_text:
                result["already_checked"] = True
                result["success"] = True
                print(f"[DEBUG] 通过按钮文本'{btn_text}'检测到已签到")
                break
        
        print(f"[PARSE] 连续签到: {result['consecutive_days']} 天")
        print(f"[PARSE] 今日奖励: {result['today_reward']} 元")
        print(f"[PARSE] 已签到状态: {result['already_checked']}")
        
    except Exception as e:
        print(f"[ERROR] 解析iframe签到结果失败: {e}")
    
    return result

def parse_checkin_result(page) -> Dict[str, Any]:
    """解析签到结果页面，优先从iframe中获取，回退到主页面"""
    # 首先尝试从iframe解析
    try:
        iframe_element = page.frame_locator("iframe").first
        if iframe_element.locator("body").count() > 0:
            print("[PARSE] 检测到iframe，使用iframe解析")
            return parse_checkin_result_from_iframe(page)
    except Exception:
        pass
    
    # 回退到原有的主页面解析逻辑
    print("[PARSE] 使用主页面解析")
    result = {
        "consecutive_days": 0,
        "today_reward": "0",
        "already_checked": False,
        "success": False
    }
    
    try:
        # 获取页面HTML内容
        html_content = page.content()
        soup = BeautifulSoup(html_content, 'html.parser')
        
        print(f"[DEBUG] 开始解析页面内容...")
        
        # 查找连续签到天数 - 多种方式
        # 方式1：通过class='streak-badge'
        streak_badge = soup.find('div', class_='streak-badge')
        if streak_badge:
            streak_text = streak_badge.get_text()
            print(f"[DEBUG] 找到streak-badge: {streak_text}")
            days_match = re.search(r'已连续签到\s*(\d+)\s*天', streak_text)
            if days_match:
                result["consecutive_days"] = int(days_match.group(1))
                print(f"[DEBUG] 解析到连续签到天数: {result['consecutive_days']}")
        
        # 方式2：直接在HTML中搜索连续签到信息
        if result["consecutive_days"] == 0:
            days_match = re.search(r'已连续签到\s*(\d+)\s*天', html_content)
            if days_match:
                result["consecutive_days"] = int(days_match.group(1))
                print(f"[DEBUG] 通过HTML搜索解析到连续签到天数: {result['consecutive_days']}")
        
        # 查找今日奖励金额 - 多种方式
        # 方式1：通过class='reward-amount'
        reward_amount = soup.find('div', class_='reward-amount')
        if reward_amount:
            reward_text = reward_amount.get_text().strip()
            print(f"[DEBUG] 找到reward-amount: {reward_text}")
            amount_match = re.search(r'([\d.]+)\s*元', reward_text)
            if amount_match:
                result["today_reward"] = amount_match.group(1)
                print(f"[DEBUG] 解析到奖励金额: {result['today_reward']}")
        
        # 方式2：直接在HTML中搜索奖励金额
        if result["today_reward"] == "0":
            # 查找类似 "0.34 元" 的模式
            amount_matches = re.findall(r'(\d+\.\d+)\s*元', html_content)
            if amount_matches:
                # 取第一个找到的金额（通常是今日奖励）
                result["today_reward"] = amount_matches[0]
                print(f"[DEBUG] 通过HTML搜索解析到奖励金额: {result['today_reward']}")
        
        # 检查是否已签到 - 多种方式
        # 方式1：检查按钮状态
        checkin_btn = soup.find('button', class_='checkin-btn')
        if checkin_btn and checkin_btn.get('disabled') is not None:
            result["already_checked"] = True
            print(f"[DEBUG] 通过按钮disabled状态检测到已签到")
        
        # 方式2：检查文本内容
        already_signed_texts = [
            "今日已签到，获得奖励",
            "今日已签到", 
            "明天再来继续领取奖励吧",
            "已签到"
        ]
        
        for text in already_signed_texts:
            if text in html_content:
                result["already_checked"] = True
                result["success"] = True
                print(f"[DEBUG] 通过文本'{text}'检测到已签到")
                break
        
        # 方式3：检查按钮文本
        buttons = soup.find_all('button')
        for btn in buttons:
            btn_text = btn.get_text().strip()
            if "已签到" in btn_text:
                result["already_checked"] = True
                result["success"] = True
                print(f"[DEBUG] 通过按钮文本'{btn_text}'检测到已签到")
                break
        
        print(f"[PARSE] 连续签到: {result['consecutive_days']} 天")
        print(f"[PARSE] 今日奖励: {result['today_reward']} 元")
        print(f"[PARSE] 已签到状态: {result['already_checked']}")
        
    except Exception as e:
        print(f"[ERROR] 解析签到结果失败: {e}")
    
    return result

def send_summary_bark_notification(user_results: List[Dict[str, Any]], bark_keys: List[str]) -> None:
    """发送汇总Bark通知"""
    if not user_results:
        print("[BARK] 无签到结果，跳过汇总通知")
        return
    
    # 统计数据
    total_users = len(user_results)
    success_users = [r for r in user_results if r["status"] == "success"]
    failed_users = [r for r in user_results if r["status"] == "failed"]
    
    success_count = len(success_users)
    failed_count = len(failed_users)
    
    # 计算总奖励
    total_reward = sum(float(r["today_reward"]) for r in success_users if r["today_reward"])
    
    # 构建通知标题
    if failed_count == 0:
        title = f"Leaflow签到完成 ✅ ({success_count}/{total_users})"
    else:
        title = f"Leaflow签到完成 ⚠️ ({success_count}/{total_users})"
    
    # 构建通知内容
    content_lines = []
    
    # 总体统计
    content_lines.append(f"📊 总计: {total_users}个用户")
    content_lines.append(f"✅ 成功: {success_count}个")
    if failed_count > 0:
        content_lines.append(f"❌ 失败: {failed_count}个")
    content_lines.append(f"💰 总奖励: {total_reward:.2f}元")
    content_lines.append("")
    
    # 成功用户详情
    if success_users:
        content_lines.append("✅ 成功用户:")
        for user in success_users:
            username = mask(user["username"])
            days = user["consecutive_days"]
            reward = user["today_reward"]
            content_lines.append(f"  {username}: {days}天, {reward}元")
    
    # 失败用户详情
    if failed_users:
        content_lines.append("")
        content_lines.append("❌ 失败用户:")
        for user in failed_users:
            username = mask(user["username"])
            reason = user["reason"]
            content_lines.append(f"  {username}: {reason}")
    
    content = "\n".join(content_lines)
    
    # 发送通知
    notification_sent = False
    
    # 优先使用用户配置的Bark Key
    for bark_key in bark_keys:
        if bark_key:
            print(f"[BARK] 使用用户Bark Key发送汇总通知...")
            if send_bark_notification(title, content, bark_key):
                notification_sent = True
                break
    
    # 如果没有用户Bark Key或发送失败，使用全局Bark Key
    if not notification_sent and BARK_KEY:
        print(f"[BARK] 使用全局Bark Key发送汇总通知...")
        send_bark_notification(title, content, BARK_KEY)
    elif not notification_sent:
        print("[BARK] 未配置Bark Key，跳过汇总通知")
    
    print(f"[BARK] 汇总通知内容:")
    print(f"标题: {title}")
    print(f"内容: {content}")

def send_bark_notification(title: str, content: str, bark_key: str = None) -> bool:
    """发送Bark通知"""
    if not bark_key:
        bark_key = BARK_KEY
        
    if bark_key:
        print(f"[BARK] 使用的Bark Key: {bark_key[:10]}...{bark_key[-10:]}")
    else:
        print("[BARK] Bark Key: None")
    
    if not bark_key:
        print("[WARN] 未配置Bark Key，跳过通知")
        return False
        
    try:
        # URL编码标题和内容
        import urllib.parse
        encoded_title = urllib.parse.quote(title)
        encoded_content = urllib.parse.quote(content)
        
        url = f"https://api.day.app/{bark_key}/{encoded_title}/{encoded_content}"
        print(f"[BARK] 请求URL: https://api.day.app/{bark_key[:10]}.../{encoded_title}/{encoded_content}")
        
        response = requests.get(url, timeout=10)
        print(f"[BARK] 响应状态码: {response.status_code}")
        print(f"[BARK] 响应内容: {response.text}")
        
        if response.status_code == 200:
            print(f"[BARK] 通知发送成功")
            return True
        elif response.status_code == 400:
            print(f"[BARK] Bark Key无效或设备不存在: {response.text}")
            print(f"[BARK] 请检查Bark Key是否正确，或在Bark App中重新获取")
            return False
        else:
            print(f"[BARK] 通知发送失败: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"[BARK] 通知发送异常: {e}")
        return False

def check_already_signed_in(page) -> bool:
    """检查是否已经签到"""
    try:
        html_content = page.content()
        
        # 检查多种已签到的标识
        already_signed_indicators = [
            "今日已签到，获得奖励",
            "今日已签到",
            "已签到",
            "明天再来继续领取奖励吧",
            "明天再来",
            "已完成签到"
        ]
        
        for indicator in already_signed_indicators:
            if indicator in html_content:
                print(f"[CHECK] 检测到已签到标识: {indicator}")
                return True
        
        # 检查是否有禁用的签到按钮
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 方法1：检查class为checkin-btn且disabled的按钮
        checkin_btn = soup.find('button', class_='checkin-btn')
        if checkin_btn and checkin_btn.get('disabled') is not None:
            print("[CHECK] 找到禁用的签到按钮，表示已签到")
            return True
        
        # 方法2：检查包含"已签到"文本的按钮
        buttons = soup.find_all('button')
        for btn in buttons:
            btn_text = btn.get_text().strip()
            if "已签到" in btn_text:
                print(f"[CHECK] 找到已签到按钮: {btn_text}")
                return True
        
        # 方法3：检查是否有奖励金额显示（表示已签到）
        reward_amount = soup.find('div', class_='reward-amount')
        if reward_amount and "元" in reward_amount.get_text():
            print(f"[CHECK] 检测到奖励金额显示，表示已签到")
            return True
            
        return False
    except Exception as e:
        print(f"[ERROR] 检查签到状态失败: {e}")
        return False

def save_result(page, meta, send_notification: bool = True):
    try: page.screenshot(path=RESULT_SCREENSHOT, full_page=True)
    except Exception: pass
    meta["screenshot"] = str(Path(RESULT_SCREENSHOT).resolve())
    
    # 解析签到结果
    checkin_result = parse_checkin_result(page)
    meta.update(checkin_result)
    
    with open(RESULT_JSON, "w", encoding="utf-8") as f:
        json.dump(meta, f, ensure_ascii=False, indent=2)
    print(f"[SAVE] 结果已保存：{RESULT_JSON}，截图：{RESULT_SCREENSHOT}")
    
    # 发送Bark通知（仅在允许时）
    if send_notification:
        print(f"[DEBUG] 检查Bark推送条件: success={checkin_result.get('success')}, already_checked={checkin_result.get('already_checked')}")
        if checkin_result.get("success") or checkin_result.get("already_checked"):
            title = "Leaflow签到成功 ✅"
            # 从meta中获取username和bark_key
            username = meta.get("username", "未知用户")
            bark_key = meta.get("bark_key", "")
            content = f"{mask(username)}连续签到{checkin_result['consecutive_days']}天，今日获得{checkin_result['today_reward']}元"
            print(f"[BARK] 准备发送通知: {title} - {content}")
            bark_result = send_bark_notification(title, content, bark_key)
            print(f"[BARK] 推送结果: {bark_result}")
        else:
            print("[BARK] 不满足推送条件，跳过Bark通知")
    else:
        print("[BARK] 跳过单用户通知，等待汇总通知")
    
    # 输出最终结果
    print("\n" + "="*50)
    print("📊 签到结果汇总")
    print("="*50)
    print(f"🔥 连续签到天数: {checkin_result['consecutive_days']} 天")
    print(f"💰 今日签到金额: {checkin_result['today_reward']} 元")
    print(f"✅ 签到状态: {'已完成' if checkin_result.get('already_checked') else '未完成'}")
    print("="*50)

def perform_checkin(username: str, password: str, bark_key: str = "", headful: bool = False, debug: bool = False, send_notification: bool = True) -> Dict[str, Any]:
    """执行单个用户的签到流程"""
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=not headful)
        context = browser.new_context()
        page = context.new_page()
        
        meta = {"ts": int(time.time()), "steps": [], "username": username, "bark_key": bark_key}
        
        try:
            # 步骤1：登录
            print(f"[STEP1] 开始登录流程，使用账号：{mask(username)}")
            page.goto(LOGIN_URL, wait_until="load")
            time.sleep(2)
            
            if not try_login(page, username, password):
                raise SystemExit("登录失败：未找到登录表单或提交未成功。可加 --headful 观察一次。")
            
            print("[STEP1] 登录成功")
            meta["steps"].append("login_success")
            time.sleep(2)
            
            # 步骤2：跳转到dashboard并点击签到按钮
            print("[STEP2] 跳转到dashboard页面{DASHBOARD_URL}...")
            page.goto(DASHBOARD_URL, wait_until="load")
            time.sleep(2)
            
            # 先检查是否已经在最终签到页面（可能已签到）
            if "checkin.leaflow.net" in page.url:
                print("[STEP2] 已在签到页面，检查签到状态...")
                if check_already_signed_in(page):
                    print("[SKIP] 今日已签到，跳过签到操作")
                    meta["skipped"] = True
                    meta["reason"] = "already_signed_in"
                    save_result(page, meta, send_notification)
                    return meta
            
            print("[STEP2] 在dashboard页面点击签到按钮...")
            if not click_dashboard_checkin_button(page, debug=debug):
                raise SystemExit("在dashboard页面点击签到按钮失败")
            
            print("[STEP2] dashboard签到按钮点击成功")
            meta["steps"].append("dashboard_click_success")
            time.sleep(3)
            
            # 步骤3：处理OAuth授权（在弹出的iframe窗口中）
            print("[STEP3] 检查是否弹出了签到窗口...")
            
            # 等待页面响应和可能的弹窗
            time.sleep(3)
            current_url = page.url
            print(f"[STEP3] 当前页面URL: {current_url}")
            
            # 检查是否有弹出的签到窗口（windows-container）
            window_container_found = False
            iframe_found = False
            
            try:
                # 检查是否有windows-container且包含内容
                windows_container = page.locator(".windows-container")
                if windows_container.count() > 0:
                    container_content = windows_container.inner_html()
                    if len(container_content.strip()) > 50:  # 有实际内容
                        window_container_found = True
                        print("[STEP3] 检测到弹出的签到窗口")
                        
                        # 检查是否有iframe
                        iframe = page.locator("iframe")
                        if iframe.count() > 0:
                            iframe_found = True
                            iframe_src = iframe.get_attribute("src")
                            print(f"[STEP3] 检测到iframe: {iframe_src}")
            except Exception as e:
                print(f"[STEP3] 检查窗口容器失败: {e}")
            
            if window_container_found and iframe_found:
                print("[STEP3] 尝试在iframe中查找授权按钮...")
                
                # 等待iframe加载
                time.sleep(2)
                
                try:
                    # 获取iframe并切换到其内容
                    iframe_element = page.frame_locator("iframe").first
                    
                    # 在iframe中查找授权按钮
                    auth_success = False
                    
                    # 方法1：使用ID选择器
                    try:
                        auth_btn = iframe_element.locator("#approve-btn")
                        if auth_btn.count() > 0:
                            auth_btn.click()
                            print("[STEP3] 在iframe中通过ID点击授权按钮成功")
                            auth_success = True
                    except Exception as e:
                        print(f"[STEP3] iframe ID选择器失败: {e}")
                    
                    # 方法2：使用class选择器
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator(".btn-approve")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过class点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe class选择器失败: {e}")
                    
                    # 方法3：使用XPath
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator("xpath=/html/body/div/div/div[2]/div[2]/button[1]")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过XPath点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe XPath选择器失败: {e}")
                    
                    # 方法4：通过文本查找
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator("button:has-text('授权')")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过文本点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe文本选择器失败: {e}")
                    
                    if auth_success:
                        print("[STEP3] iframe授权成功")
                        meta["steps"].append("iframe_oauth_success")
                        time.sleep(3)
                    else:
                        print("[STEP3] iframe授权失败，尝试继续流程")
                        meta["steps"].append("iframe_oauth_failed")
                        
                except Exception as e:
                    print(f"[STEP3] iframe处理失败: {e}")
                    meta["steps"].append("iframe_error")
            else:
                print("[STEP3] 未检测到弹出窗口或iframe，可能无需授权")
                meta["steps"].append("no_popup_window")
            
            # 步骤4：在iframe中的签到页面点击签到按钮
            print("[STEP4] 在iframe签到页面点击签到按钮...")
            
            # 等待iframe内容加载
            time.sleep(2)
            
            try:
                # 获取iframe
                iframe_element = page.frame_locator("iframe").first
                
                # 先检查iframe中是否已经签到
                iframe_already_signed = False
                try:
                    iframe_content = iframe_element.locator("body").inner_html()
                    if "今日已签到" in iframe_content or "已签到" in iframe_content or "明天再来" in iframe_content:
                        iframe_already_signed = True
                        print("[STEP4] iframe中检测到已签到状态")
                except Exception:
                    pass
                
                if iframe_already_signed:
                    print("[STEP4] iframe中已签到，跳过点击")
                    meta["steps"].append("iframe_already_signed")
                else:
                    # 在iframe中点击签到按钮
                    final_click_success = False
                    
                    # 方法1：使用XPath
                    try:
                        checkin_btn = iframe_element.locator("xpath=/html/body/div/div/div[1]/div/div[3]/button")
                        if checkin_btn.count() > 0:
                            checkin_btn.click()
                            print("[STEP4] 在iframe中通过XPath点击签到按钮成功")
                            final_click_success = True
                    except Exception as e:
                        print(f"[STEP4] iframe XPath选择器失败: {e}")
                    
                    # 方法2：通过文本查找
                    if not final_click_success:
                        try:
                            checkin_btn = iframe_element.locator("button:has-text('签到')")
                            if checkin_btn.count() > 0:
                                checkin_btn.click()
                                print("[STEP4] 在iframe中通过文本点击签到按钮成功")
                                final_click_success = True
                        except Exception as e:
                            print(f"[STEP4] iframe文本选择器失败: {e}")
                    
                    # 方法3：查找未禁用的签到按钮
                    if not final_click_success:
                        try:
                            checkin_btns = iframe_element.locator("button")
                            for i in range(checkin_btns.count()):
                                btn = checkin_btns.nth(i)
                                btn_text = btn.inner_text()
                                is_disabled = btn.get_attribute("disabled")
                                if "签到" in btn_text and not is_disabled:
                                    btn.click()
                                    print(f"[STEP4] 在iframe中点击签到按钮成功: {btn_text}")
                                    final_click_success = True
                                    break
                        except Exception as e:
                            print(f"[STEP4] iframe遍历按钮失败: {e}")
                    
                    if final_click_success:
                        print("[STEP4] iframe签到按钮点击成功")
                        meta["steps"].append("iframe_final_click_success")
                        time.sleep(3)
                    else:
                        print("[STEP4] iframe签到按钮点击失败")
                        meta["steps"].append("iframe_final_click_failed")
                        
            except Exception as e:
                print(f"[STEP4] iframe签到处理失败: {e}")
                meta["steps"].append("iframe_checkin_error")
            
            # 步骤5：等待并解析iframe中的签到结果
            print("[STEP5] 等待iframe中的签到结果...")
            time.sleep(3)
            
            # 从iframe中解析结果
            try:
                iframe_element = page.frame_locator("iframe").first
                
                # 等待iframe内容更新
                time.sleep(2)
                
                # 创建一个临时页面对象来复用解析函数
                # 这里我们需要获取iframe的内容并解析
                iframe_content = iframe_element.locator("body").inner_html()
                
                # 将iframe内容写入主页面进行解析（临时方案）
                page.evaluate("""
                    () => {{
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = `{}`;
                        tempDiv.id = 'iframe-content-temp';
                        document.body.appendChild(tempDiv);
                    }}
                """.format(iframe_content.replace('`', '\\`')))
                
                print("[STEP5] 已提取iframe内容进行解析")
                meta["steps"].append("iframe_content_extracted")
                
            except Exception as e:
                print(f"[STEP5] iframe内容提取失败: {e}")
                meta["steps"].append("iframe_extract_failed")
            
            meta["completed"] = True
            save_result(page, meta, send_notification)
            
            # 更新用户最后签到时间
            update_user_last_checkin(username)
            
            return meta
            
        except Exception as e:
            print(f"[ERROR] 签到流程出错: {e}")
            meta["error"] = str(e)
            save_result(page, meta, send_notification)
            return meta
        finally:
            browser.close()

def main():
    global BARK_KEY
    
    parser = argparse.ArgumentParser(description="Leaflow 多用户自动签到工具")
    
    # 用户管理命令
    parser.add_argument("--add-user", action="store_true", help="添加新用户")
    parser.add_argument("--remove-user", help="删除指定用户")
    parser.add_argument("--list-users", action="store_true", help="列出所有用户")
    
    # 签到相关参数
    parser.add_argument("run_password", nargs="?", help="运行密码（用于解密账号密码）")
    parser.add_argument("--username", "-u", help="指定签到的用户名（不指定则为所有用户签到）")
    parser.add_argument("--headful", action="store_true", help="调试用：有界面模式")
    parser.add_argument("--debug", action="store_true", help="调试模式：输出详细的页面信息")
    
    # 兼容旧版本的参数（用于单用户模式）
    parser.add_argument("--old-username", help="旧版本兼容：账号")
    parser.add_argument("--old-password", help="旧版本兼容：密码")
    parser.add_argument("--bark-key", help="旧版本兼容：Bark通知Key")
    
    args = parser.parse_args()
    
    # 用户管理功能
    if args.add_user:
        print("=== 添加新用户 ===")
        username = input("请输入用户名: ").strip()
        if not username:
            print("[ERROR] 用户名不能为空")
            return
        
        password = input("请输入密码: ").strip()
        if not password:
            print("[ERROR] 密码不能为空")
            return
        
        run_password = input("请设置运行密码（用于加密保存）: ").strip()
        if not run_password:
            print("[ERROR] 运行密码不能为空")
            return
        
        bark_key = input("请输入Bark Key（可选，直接回车跳过）: ").strip()
        
        if add_user(username, password, run_password, bark_key):
            print(f"[SUCCESS] 用户 {mask(username)} 添加成功！")
        return
    
    if args.remove_user:
        if remove_user(args.remove_user):
            print(f"[SUCCESS] 用户删除成功")
        return
    
    if args.list_users:
        list_users()
        return
    
    # 兼容旧版本的单用户模式
    if args.old_username and args.old_password:
        print("[INFO] 检测到旧版本参数，使用单用户模式")
        if args.bark_key or os.getenv("BARK_KEY"):
            BARK_KEY = args.bark_key or os.getenv("BARK_KEY")
        
        result = perform_checkin(
            username=args.old_username,
            password=args.old_password,
            bark_key=BARK_KEY,
            headful=args.headful,
            debug=args.debug
        )
        return
    
    # 多用户签到模式
    if not args.run_password:
        print("[ERROR] 请提供运行密码")
        print("用法:")
        print("  签到所有用户: python checkin.py <运行密码>")
        print("  签到指定用户: python checkin.py <运行密码> --username <用户名>")
        print("  添加用户: python checkin.py --add-user")
        print("  列出用户: python checkin.py --list-users")
        print("  删除用户: python checkin.py --remove-user <用户名>")
        return
    
    config = load_users_config()
    if not config["users"]:
        print("[ERROR] 暂无用户配置，请先使用 --add-user 添加用户")
        return
    
    # 确定要签到的用户列表
    target_users = []
    if args.username:
        # 签到指定用户
        for user in config["users"]:
            if user["username"] == args.username:
                target_users.append(user)
                break
        if not target_users:
            print(f"[ERROR] 用户 {mask(args.username)} 不存在")
            return
    else:
        # 签到所有用户
        target_users = config["users"]
    
    print(f"[INFO] 开始为 {len(target_users)} 个用户执行签到...")
    print("=" * 60)
    
    success_count = 0
    failed_count = 0
    user_results = []  # 收集所有用户的签到结果
    bark_keys = []     # 收集所有用户的Bark Key
    
    for i, user in enumerate(target_users, 1):
        username = user["username"]
        print(f"\n[{i}/{len(target_users)}] 开始为用户 {mask(username)} 签到...")
        
        try:
            # 获取用户凭据
            credentials = get_user_credentials(username, args.run_password)
            if not credentials:
                print(f"[ERROR] 获取用户 {mask(username)} 凭据失败")
                failed_count += 1
                user_results.append({
                    "username": username,
                    "status": "failed",
                    "reason": "凭据获取失败",
                    "consecutive_days": 0,
                    "today_reward": "0"
                })
                continue
            
            username, password, bark_key = credentials
            
            # 收集Bark Key（用于后续统一通知）
            if bark_key and bark_key not in bark_keys:
                bark_keys.append(bark_key)
            
            # 执行签到（不发送单独通知）
            result = perform_checkin(
                username=username,
                password=password,
                bark_key=bark_key,
                headful=args.headful,
                debug=args.debug,
                send_notification=False  # 不发送单独通知
            )
            
            # 收集结果
            if result.get("completed") or result.get("skipped"):
                success_count += 1
                print(f"[SUCCESS] 用户 {mask(username)} 签到完成")
                user_results.append({
                    "username": username,
                    "status": "success",
                    "reason": "已签到" if result.get("skipped") else "签到成功",
                    "consecutive_days": result.get("consecutive_days", 0),
                    "today_reward": result.get("today_reward", "0")
                })
            else:
                failed_count += 1
                print(f"[FAILED] 用户 {mask(username)} 签到失败")
                user_results.append({
                    "username": username,
                    "status": "failed",
                    "reason": result.get("error", "签到失败"),
                    "consecutive_days": result.get("consecutive_days", 0),
                    "today_reward": result.get("today_reward", "0")
                })
                
        except Exception as e:
            print(f"[ERROR] 用户 {mask(username)} 签到异常: {e}")
            failed_count += 1
            user_results.append({
                "username": username,
                "status": "failed",
                "reason": f"异常: {str(e)}",
                "consecutive_days": 0,
                "today_reward": "0"
            })
        
        # 用户间间隔
        if i < len(target_users):
            print(f"[INFO] 等待 3 秒后处理下一个用户...")
            time.sleep(3)
    
    # 输出总结
    print("\n" + "=" * 60)
    print("📊 签到任务完成")
    print("=" * 60)
    print(f"✅ 成功: {success_count} 个用户")
    print(f"❌ 失败: {failed_count} 个用户")
    print(f"📝 总计: {len(target_users)} 个用户")
    print("=" * 60)
    
    # 发送汇总Bark通知
    send_summary_bark_notification(user_results, bark_keys)

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=not args.headful)
        context = browser.new_context()
        page = context.new_page()
        
        meta = {"ts": int(time.time()), "steps": [], "username": username}
        
        try:
            # 步骤1：登录
            print(f"[STEP1] 开始登录流程，使用账号：{mask(username)}")
            page.goto(LOGIN_URL, wait_until="load")
            time.sleep(2)
            
            if not try_login(page, username, password):
                raise SystemExit("登录失败：未找到登录表单或提交未成功。可加 --headful 观察一次。")
            
            print("[STEP1] 登录成功")
            meta["steps"].append("login_success")
            time.sleep(2)
            
            # 步骤2：跳转到dashboard并点击签到按钮
            print("[STEP2] 跳转到dashboard页面...")
            page.goto(DASHBOARD_URL, wait_until="load")
            time.sleep(2)
            
            # 先检查是否已经在最终签到页面（可能已签到）
            if "checkin.leaflow.net" in page.url:
                print("[STEP2] 已在签到页面，检查签到状态...")
                if check_already_signed_in(page):
                    print("[SKIP] 今日已签到，跳过签到操作")
                    meta["skipped"] = True
                    meta["reason"] = "already_signed_in"
                    save_result(page, meta)
                    browser.close()
                    return
            
            print("[STEP2] 在dashboard页面点击签到按钮...")
            if not click_dashboard_checkin_button(page, debug=args.debug):
                raise SystemExit("在dashboard页面点击签到按钮失败")
            
            print("[STEP2] dashboard签到按钮点击成功")
            meta["steps"].append("dashboard_click_success")
            time.sleep(3)
            
            # 步骤3：处理OAuth授权（在弹出的iframe窗口中）
            print("[STEP3] 检查是否弹出了签到窗口...")
            
            # 等待页面响应和可能的弹窗
            time.sleep(3)
            current_url = page.url
            print(f"[STEP3] 当前页面URL: {current_url}")
            
            # 检查是否有弹出的签到窗口（windows-container）
            window_container_found = False
            iframe_found = False
            
            try:
                # 检查是否有windows-container且包含内容
                windows_container = page.locator(".windows-container")
                if windows_container.count() > 0:
                    container_content = windows_container.inner_html()
                    if len(container_content.strip()) > 50:  # 有实际内容
                        window_container_found = True
                        print("[STEP3] 检测到弹出的签到窗口")
                        
                        # 检查是否有iframe
                        iframe = page.locator("iframe")
                        if iframe.count() > 0:
                            iframe_found = True
                            iframe_src = iframe.get_attribute("src")
                            print(f"[STEP3] 检测到iframe: {iframe_src}")
            except Exception as e:
                print(f"[STEP3] 检查窗口容器失败: {e}")
            
            if window_container_found and iframe_found:
                print("[STEP3] 尝试在iframe中查找授权按钮...")
                
                # 等待iframe加载
                time.sleep(2)
                
                try:
                    # 获取iframe并切换到其内容
                    iframe_element = page.frame_locator("iframe").first
                    
                    # 在iframe中查找授权按钮
                    auth_success = False
                    
                    # 方法1：使用ID选择器
                    try:
                        auth_btn = iframe_element.locator("#approve-btn")
                        if auth_btn.count() > 0:
                            auth_btn.click()
                            print("[STEP3] 在iframe中通过ID点击授权按钮成功")
                            auth_success = True
                    except Exception as e:
                        print(f"[STEP3] iframe ID选择器失败: {e}")
                    
                    # 方法2：使用class选择器
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator(".btn-approve")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过class点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe class选择器失败: {e}")
                    
                    # 方法3：使用XPath
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator("xpath=/html/body/div/div/div[2]/div[2]/button[1]")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过XPath点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe XPath选择器失败: {e}")
                    
                    # 方法4：通过文本查找
                    if not auth_success:
                        try:
                            auth_btn = iframe_element.locator("button:has-text('授权')")
                            if auth_btn.count() > 0:
                                auth_btn.click()
                                print("[STEP3] 在iframe中通过文本点击授权按钮成功")
                                auth_success = True
                        except Exception as e:
                            print(f"[STEP3] iframe文本选择器失败: {e}")
                    
                    if auth_success:
                        print("[STEP3] iframe授权成功")
                        meta["steps"].append("iframe_oauth_success")
                        time.sleep(3)
                    else:
                        print("[STEP3] iframe授权失败，尝试继续流程")
                        meta["steps"].append("iframe_oauth_failed")
                        
                except Exception as e:
                    print(f"[STEP3] iframe处理失败: {e}")
                    meta["steps"].append("iframe_error")
            else:
                print("[STEP3] 未检测到弹出窗口或iframe，可能无需授权")
                meta["steps"].append("no_popup_window")
            
            # 步骤4：在iframe中的签到页面点击签到按钮
            print("[STEP4] 在iframe签到页面点击签到按钮...")
            
            # 等待iframe内容加载
            time.sleep(2)
            
            try:
                # 获取iframe
                iframe_element = page.frame_locator("iframe").first
                
                # 先检查iframe中是否已经签到
                iframe_already_signed = False
                try:
                    iframe_content = iframe_element.locator("body").inner_html()
                    if "今日已签到" in iframe_content or "已签到" in iframe_content or "明天再来" in iframe_content:
                        iframe_already_signed = True
                        print("[STEP4] iframe中检测到已签到状态")
                except Exception:
                    pass
                
                if iframe_already_signed:
                    print("[STEP4] iframe中已签到，跳过点击")
                    meta["steps"].append("iframe_already_signed")
                else:
                    # 在iframe中点击签到按钮
                    final_click_success = False
                    
                    # 方法1：使用XPath
                    try:
                        checkin_btn = iframe_element.locator("xpath=/html/body/div/div/div[1]/div/div[3]/button")
                        if checkin_btn.count() > 0:
                            checkin_btn.click()
                            print("[STEP4] 在iframe中通过XPath点击签到按钮成功")
                            final_click_success = True
                    except Exception as e:
                        print(f"[STEP4] iframe XPath选择器失败: {e}")
                    
                    # 方法2：通过文本查找
                    if not final_click_success:
                        try:
                            checkin_btn = iframe_element.locator("button:has-text('签到')")
                            if checkin_btn.count() > 0:
                                checkin_btn.click()
                                print("[STEP4] 在iframe中通过文本点击签到按钮成功")
                                final_click_success = True
                        except Exception as e:
                            print(f"[STEP4] iframe文本选择器失败: {e}")
                    
                    # 方法3：查找未禁用的签到按钮
                    if not final_click_success:
                        try:
                            checkin_btns = iframe_element.locator("button")
                            for i in range(checkin_btns.count()):
                                btn = checkin_btns.nth(i)
                                btn_text = btn.inner_text()
                                is_disabled = btn.get_attribute("disabled")
                                if "签到" in btn_text and not is_disabled:
                                    btn.click()
                                    print(f"[STEP4] 在iframe中点击签到按钮成功: {btn_text}")
                                    final_click_success = True
                                    break
                        except Exception as e:
                            print(f"[STEP4] iframe遍历按钮失败: {e}")
                    
                    if final_click_success:
                        print("[STEP4] iframe签到按钮点击成功")
                        meta["steps"].append("iframe_final_click_success")
                        time.sleep(3)
                    else:
                        print("[STEP4] iframe签到按钮点击失败")
                        meta["steps"].append("iframe_final_click_failed")
                        
            except Exception as e:
                print(f"[STEP4] iframe签到处理失败: {e}")
                meta["steps"].append("iframe_checkin_error")
            
            # 步骤5：等待并解析iframe中的签到结果
            print("[STEP5] 等待iframe中的签到结果...")
            time.sleep(3)
            
            # 从iframe中解析结果
            try:
                iframe_element = page.frame_locator("iframe").first
                
                # 等待iframe内容更新
                time.sleep(2)
                
                # 创建一个临时页面对象来复用解析函数
                # 这里我们需要获取iframe的内容并解析
                iframe_content = iframe_element.locator("body").inner_html()
                
                # 将iframe内容写入主页面进行解析（临时方案）
                escaped_content = iframe_content.replace('`', '\\`')  # 方法 2
                page.evaluate(f"""
                    () => {{
                        const tempDiv = document.createElement('div');
                        tempDiv.innerHTML = `{escaped_content}`;
                        tempDiv.id = 'iframe-content-temp';
                        document.body.appendChild(tempDiv);
                    }}
                """)
                
                print("[STEP5] 已提取iframe内容进行解析")
                meta["steps"].append("iframe_content_extracted")
                
            except Exception as e:
                print(f"[STEP5] iframe内容提取失败: {e}")
                meta["steps"].append("iframe_extract_failed")
            
            meta["completed"] = True
            save_result(page, meta)
            
        except Exception as e:
            print(f"[ERROR] 签到流程出错: {e}")
            meta["error"] = str(e)
            save_result(page, meta)
        finally:
            browser.close()

if __name__ == "__main__":
    main()
